export default [
  "strapi::errors",
  {
    name: "strapi::security",
    config: {
      contentSecurityPolicy: {
        directives: {
          "script-src": ["'self'", "'unsafe-inline'", "cdn.marutitech.com"],
          "img-src": [
            "'self'",
            "data:",
            "strapi.io",
            "cdn.marutitech.com",
            "storage.googleapis.com",
            "cdn-gcp.new.marutitech.com",
          ],
          "media-src": ["cdn.marutitech.com", "cdn-gcp.new.marutitech.com"],
        },
      },
    },
  },
  "strapi::cors",
  "strapi::poweredBy",
  "strapi::logger",
  "strapi::query",
  {
    name: "strapi::body",
    config: {
      jsonLimit: "500mb",
      formLimit: "500mb",
      textLimit: "500mb",
    },
  },
  "strapi::session",
  "strapi::favicon",
  "strapi::public",
];
