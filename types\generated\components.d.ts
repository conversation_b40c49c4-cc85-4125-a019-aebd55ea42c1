import type { Schema, Struct } from '@strapi/strapi';

export interface AiReadinessOptions extends Struct.ComponentSchema {
  collectionName: 'components_ai_readiness_options';
  info: {
    displayName: 'options';
  };
  attributes: {
    name: Schema.Attribute.String;
    value: Schema.Attribute.Integer;
  };
}

export interface AiReadinessQuestion extends Struct.ComponentSchema {
  collectionName: 'components_ai_readiness_questions';
  info: {
    description: '';
    displayName: 'question';
  };
  attributes: {
    answers: Schema.Attribute.Component<'ai-readiness.options', true>;
    name: Schema.Attribute.String;
    number: Schema.Attribute.Integer;
    sub_question: Schema.Attribute.Component<'ai-readiness.options', true>;
    type: Schema.Attribute.Enumeration<['draggable', 'mcq']>;
  };
}

export interface AllServicePageAllServicePage extends Struct.ComponentSchema {
  collectionName: 'components_all_service_page_all_service_pages';
  info: {
    displayName: 'AllServicePage';
  };
  attributes: {
    button: Schema.Attribute.Component<'common.button', false>;
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    l_2_service_pages: Schema.Attribute.Relation<
      'oneToMany',
      'api::l2-service-page.l2-service-page'
    >;
    tag: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface AuditMethodologyAuditMethodology
  extends Struct.ComponentSchema {
  collectionName: 'components_audit_methodology_audit_methodologies';
  info: {
    description: '';
    displayName: 'Audit-methodology';
  };
  attributes: {
    box: Schema.Attribute.Component<'common.title-description', true>;
    ctaButtonText: Schema.Attribute.String;
    description: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
  };
}

export interface BenefitsBenefits extends Struct.ComponentSchema {
  collectionName: 'components_benefits_benefits';
  info: {
    displayName: 'Benefits';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    first_slider: Schema.Attribute.Component<
      'common.title-description-image',
      true
    >;
    second_slider: Schema.Attribute.Component<
      'common.title-description-image',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface BlogBlogRelatedService extends Struct.ComponentSchema {
  collectionName: 'components_blog_blog_related_services';
  info: {
    description: '';
    displayName: 'blog_related_service';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
    url: Schema.Attribute.String;
  };
}

export interface BlogCaseStudySuggestions extends Struct.ComponentSchema {
  collectionName: 'components_blog_case_study_suggestions';
  info: {
    description: '';
    displayName: 'caseStudy_suggestions';
    icon: 'ad';
  };
  attributes: {
    cover_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    link: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface BlogContent extends Struct.ComponentSchema {
  collectionName: 'components_blog_contents';
  info: {
    description: '';
    displayName: 'content';
    icon: 'align-justify';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
    twitter_link: Schema.Attribute.String;
    twitter_link_text: Schema.Attribute.String;
  };
}

export interface BlogSuggestions extends Struct.ComponentSchema {
  collectionName: 'components_blog_suggestions';
  info: {
    description: '';
    displayName: 'suggestions';
    icon: 'th';
  };
  attributes: {
    blogs: Schema.Attribute.Relation<'oneToMany', 'api::blog.blog'>;
  };
}

export interface CareersCoreValues extends Struct.ComponentSchema {
  collectionName: 'components_careers_core_values';
  info: {
    displayName: 'core_values';
  };
  attributes: {
    box: Schema.Attribute.Component<'common.title-description-image', true>;
    description: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
  };
}

export interface CareersLifeAtMtl extends Struct.ComponentSchema {
  collectionName: 'components_careers_life_at_mtls';
  info: {
    description: '';
    displayName: 'life_at_mtl';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    images: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface CaseStudiesHeroSection extends Struct.ComponentSchema {
  collectionName: 'components_case_studies_hero_sections';
  info: {
    description: '';
    displayName: 'hero_section';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    download_button: Schema.Attribute.Component<'common.button', false>;
    global_industries: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-industry.global-industry'
    >;
    global_services: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-service.global-service'
    >;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    tag: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface CaseStudiesPreview extends Struct.ComponentSchema {
  collectionName: 'components_case_studies_previews';
  info: {
    description: '';
    displayName: 'preview';
  };
  attributes: {
    link: Schema.Attribute.String;
    preview_background_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    tag: Schema.Attribute.String;
    title: Schema.Attribute.Text;
  };
}

export interface CaseStudiesQuote extends Struct.ComponentSchema {
  collectionName: 'components_case_studies_quotes';
  info: {
    description: '';
    displayName: 'quote';
  };
  attributes: {
    quote_by: Schema.Attribute.String;
    rich_text: Schema.Attribute.RichText;
  };
}

export interface CaseStudyCardBox extends Struct.ComponentSchema {
  collectionName: 'components_case_study_card_boxes';
  info: {
    description: '';
    displayName: 'cardBox';
  };
  attributes: {
    case_study_badge: Schema.Attribute.String;
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
    url: Schema.Attribute.String;
  };
}

export interface CaseStudyCaseStudy extends Struct.ComponentSchema {
  collectionName: 'components_case_study_case_studies';
  info: {
    description: '';
    displayName: 'Case-study';
  };
  attributes: {
    card_box: Schema.Attribute.Component<'case-study.card-box', true>;
    link_title: Schema.Attribute.String;
    title: Schema.Attribute.String;
    url: Schema.Attribute.String;
  };
}

export interface CaseStudyCaseStudyRelation extends Struct.ComponentSchema {
  collectionName: 'components_case_study_case_study_relations';
  info: {
    description: '';
    displayName: 'case_study_relation';
  };
  attributes: {
    case_study_relation: Schema.Attribute.Relation<
      'oneToMany',
      'api::case-study.case-study'
    >;
    link_title: Schema.Attribute.String;
    link_url: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface CaseStudyFilters extends Struct.ComponentSchema {
  collectionName: 'components_case_study_filters';
  info: {
    description: '';
    displayName: 'filters';
  };
  attributes: {
    search_button_title: Schema.Attribute.String;
    select_industries_title: Schema.Attribute.String;
    select_services_title: Schema.Attribute.String;
    sort_by_text: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface CaseStudyHeroSection extends Struct.ComponentSchema {
  collectionName: 'components_case_study_hero_sections';
  info: {
    description: '';
    displayName: 'hero_section';
  };
  attributes: {
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
  };
}

export interface CollabCollab extends Struct.ComponentSchema {
  collectionName: 'components_collab_collabs';
  info: {
    description: '';
    displayName: 'collab';
  };
  attributes: {
    title: Schema.Attribute.String;
  };
}

export interface CommonBlogHero extends Struct.ComponentSchema {
  collectionName: 'components_common_blog_heroes';
  info: {
    description: '';
    displayName: 'blog_hero';
  };
  attributes: {
    blog_tag: Schema.Attribute.String;
    description: Schema.Attribute.RichText;
    global_industries: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-industry.global-industry'
    >;
    global_services: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-service.global-service'
    >;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
  };
}

export interface CommonButton extends Struct.ComponentSchema {
  collectionName: 'components_common_buttons';
  info: {
    displayName: 'Button';
  };
  attributes: {
    link: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface CommonButtonWithImage extends Struct.ComponentSchema {
  collectionName: 'components_common_button_with_images';
  info: {
    displayName: 'ButtonWithImage';
  };
  attributes: {
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    link: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface CommonClutchReviews extends Struct.ComponentSchema {
  collectionName: 'components_common_clutch_reviews';
  info: {
    displayName: 'clutch_reviews';
  };
  attributes: {
    review_image: Schema.Attribute.Media<'images', true>;
    title: Schema.Attribute.String;
  };
}

export interface CommonFilter extends Struct.ComponentSchema {
  collectionName: 'components_common_filters';
  info: {
    displayName: 'filter';
  };
  attributes: {};
}

export interface CommonLinkBox extends Struct.ComponentSchema {
  collectionName: 'components_common_link_boxes';
  info: {
    description: '';
    displayName: 'LinkBox';
  };
  attributes: {
    link: Schema.Attribute.String;
    Sublinks: Schema.Attribute.Component<'common.sublinks', true>;
    title: Schema.Attribute.String;
  };
}

export interface CommonMultipleImage extends Struct.ComponentSchema {
  collectionName: 'components_common_multiple_images';
  info: {
    displayName: 'MultipleImage';
  };
  attributes: {
    images: Schema.Attribute.Media<'images', true>;
  };
}

export interface CommonSingleImage extends Struct.ComponentSchema {
  collectionName: 'components_common_single_images';
  info: {
    displayName: 'SingleImage';
  };
  attributes: {
    image: Schema.Attribute.Media<'images'>;
  };
}

export interface CommonSublinks extends Struct.ComponentSchema {
  collectionName: 'components_common_sublinks';
  info: {
    description: '';
    displayName: 'Sublinks';
  };
  attributes: {
    link: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface CommonTitle extends Struct.ComponentSchema {
  collectionName: 'components_common_titles';
  info: {
    description: '';
    displayName: 'Title';
  };
  attributes: {
    email_id: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface CommonTitleDescImageLink extends Struct.ComponentSchema {
  collectionName: 'components_common_title_desc_image_links';
  info: {
    description: '';
    displayName: 'TitleDescImageLink';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    link: Schema.Attribute.String;
    mobile_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    title: Schema.Attribute.String;
  };
}

export interface CommonTitleDescription extends Struct.ComponentSchema {
  collectionName: 'components_common_title_descriptions';
  info: {
    displayName: 'TitleDescription';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
  };
}

export interface CommonTitleDescriptionImage extends Struct.ComponentSchema {
  collectionName: 'components_common_title_description_images';
  info: {
    description: '';
    displayName: 'TitleDescriptionImage';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    mobile_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    title: Schema.Attribute.String;
  };
}

export interface CommonTitleDescriptionImageButton
  extends Struct.ComponentSchema {
  collectionName: 'components_common_title_description_image_buttons';
  info: {
    description: '';
    displayName: 'TitleDescriptionImageButton';
  };
  attributes: {
    button_link: Schema.Attribute.String;
    button_title: Schema.Attribute.String;
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    mobile_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    title: Schema.Attribute.String;
  };
}

export interface CommonTitleDescriptionImageSlider
  extends Struct.ComponentSchema {
  collectionName: 'components_common_title_description_image_sliders';
  info: {
    description: '';
    displayName: 'titleDescriptionImageSlider';
  };
  attributes: {
    clientDescription: Schema.Attribute.RichText;
    clientName: Schema.Attribute.String;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    testimonial_video_link: Schema.Attribute.String;
  };
}

export interface CommonTitleImage extends Struct.ComponentSchema {
  collectionName: 'components_common_title_images';
  info: {
    displayName: 'TitleImage';
  };
  attributes: {
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
  };
}

export interface CommonTitleImageRepeat extends Struct.ComponentSchema {
  collectionName: 'components_common_title_image_repeats';
  info: {
    description: '';
    displayName: 'titleImageRepeat';
  };
  attributes: {
    emp_details: Schema.Attribute.Component<
      'common.title-description-image',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface CompanyStatisticsCompanyStatistics
  extends Struct.ComponentSchema {
  collectionName: 'components_company_statistics_company_statistics';
  info: {
    description: '';
    displayName: 'Company Statistics';
  };
  attributes: {
    statisticsCards: Schema.Attribute.Component<
      'company-statistics.statistics-cards',
      true
    >;
    Title: Schema.Attribute.String;
  };
}

export interface CompanyStatisticsStatisticsCards
  extends Struct.ComponentSchema {
  collectionName: 'components_company_statistics_statistics_cards';
  info: {
    description: '';
    displayName: 'Statistics Cards';
  };
  attributes: {
    box_title: Schema.Attribute.String;
    decimalValue: Schema.Attribute.Boolean;
    description: Schema.Attribute.Text;
    numbersOfDigitAfterDecimal: Schema.Attribute.Integer;
    statistics: Schema.Attribute.Float;
    suffix: Schema.Attribute.String;
  };
}

export interface ContactUsFormContactUsForm extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_form_contact_us_forms';
  info: {
    description: '';
    displayName: 'Contact-us-form';
  };
  attributes: {
    button_title: Schema.Attribute.String;
    consent_text: Schema.Attribute.String;
    description: Schema.Attribute.RichText;
    left_side_fields: Schema.Attribute.Component<'form.form-fields', false>;
    title: Schema.Attribute.String;
  };
}

export interface ContactUsFormRightSideContent extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_form_right_side_contents';
  info: {
    description: '';
    displayName: 'right_side_content';
  };
  attributes: {
    collab_box: Schema.Attribute.Component<'common.title', true>;
    collabration_title: Schema.Attribute.String;
    form_awards: Schema.Attribute.Component<'form-awards.form-awards', false>;
    logo: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    our_offices: Schema.Attribute.Component<'our-offices.our-offices', false>;
    request_a_discovery_call: Schema.Attribute.String;
    request_a_discovery_call_box: Schema.Attribute.Component<
      'common.title',
      true
    >;
    trusted_by_logos: Schema.Attribute.Component<
      'common.multiple-image',
      false
    >;
    trusted_by_title: Schema.Attribute.String;
  };
}

export interface CtaCta extends Struct.ComponentSchema {
  collectionName: 'components_cta_ctas';
  info: {
    description: '';
    displayName: 'CTA';
  };
  attributes: {
    ctaButtonText: Schema.Attribute.String;
    ctaLink: Schema.Attribute.String;
    ctaTitle: Schema.Attribute.RichText;
  };
}

export interface EbooksPreview extends Struct.ComponentSchema {
  collectionName: 'components_ebooks_previews';
  info: {
    description: '';
    displayName: 'preview';
  };
  attributes: {
    link: Schema.Attribute.String & Schema.Attribute.Required;
    preview_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    title: Schema.Attribute.String;
  };
}

export interface EmployeeTestimonialEmployeeBox extends Struct.ComponentSchema {
  collectionName: 'components_employee_testimonial_employee_boxes';
  info: {
    displayName: 'employee_box';
  };
  attributes: {
    box_description: Schema.Attribute.RichText;
    emp_description: Schema.Attribute.String;
    emp_image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    emp_title: Schema.Attribute.String;
  };
}

export interface EmployeeTestimonialEmployeeTestimonial
  extends Struct.ComponentSchema {
  collectionName: 'components_employee_testimonial_employee_testimonials';
  info: {
    description: '';
    displayName: 'Employee-testimonial';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    employee_box: Schema.Attribute.Component<
      'employee-testimonial.employee-box',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface EventsPagesCard extends Struct.ComponentSchema {
  collectionName: 'components_events_pages_cards';
  info: {
    displayName: 'card';
  };
  attributes: {
    card_description: Schema.Attribute.RichText;
    card_title: Schema.Attribute.String;
  };
}

export interface EventsPagesHeroSection extends Struct.ComponentSchema {
  collectionName: 'components_events_pages_hero_sections';
  info: {
    description: '';
    displayName: 'hero_section';
  };
  attributes: {
    booth_number_title: Schema.Attribute.String;
    booth_number_value: Schema.Attribute.String;
    button: Schema.Attribute.Component<'common.button', false>;
    date_title: Schema.Attribute.String;
    date_value: Schema.Attribute.String;
    event_starting_date: Schema.Attribute.Date;
    hero_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    hero_title: Schema.Attribute.String;
    venue_title: Schema.Attribute.String;
    venue_value: Schema.Attribute.String;
  };
}

export interface EventsPagesOfferingsCard extends Struct.ComponentSchema {
  collectionName: 'components_events_pages_offerings_cards';
  info: {
    description: '';
    displayName: 'offerings_card';
  };
  attributes: {
    card: Schema.Attribute.Component<'events-pages.card', true>;
    title: Schema.Attribute.String;
  };
}

export interface EventsPagesOurPeople extends Struct.ComponentSchema {
  collectionName: 'components_events_pages_our_people';
  info: {
    displayName: 'our_people';
  };
  attributes: {
    our_people: Schema.Attribute.Component<
      'events-pages.our-people-card',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface EventsPagesOurPeopleCard extends Struct.ComponentSchema {
  collectionName: 'components_events_pages_our_people_cards';
  info: {
    description: '';
    displayName: 'our_people_card';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    link: Schema.Attribute.String;
    logo: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
  };
}

export interface FaqFaq extends Struct.ComponentSchema {
  collectionName: 'components_faq_faqs';
  info: {
    displayName: 'faq';
  };
  attributes: {
    faq_items: Schema.Attribute.Component<'faq.faq-items', true>;
    title: Schema.Attribute.String;
  };
}

export interface FaqFaqItems extends Struct.ComponentSchema {
  collectionName: 'components_faq_faq_items';
  info: {
    description: '';
    displayName: 'faq_items';
  };
  attributes: {
    answer: Schema.Attribute.RichText;
    question: Schema.Attribute.String;
  };
}

export interface FooterFourthRow extends Struct.ComponentSchema {
  collectionName: 'components_footer_fourth_rows';
  info: {
    description: '';
    displayName: 'CompanyLogoRow';
  };
  attributes: {
    Copyright: Schema.Attribute.Text;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    link: Schema.Attribute.String;
    social_platforms: Schema.Attribute.Component<
      'footer.social-platforms',
      true
    >;
  };
}

export interface FooterSectorBox extends Struct.ComponentSchema {
  collectionName: 'components_footer_sector_boxes';
  info: {
    description: '';
    displayName: 'SubSectorBox';
  };
  attributes: {
    link: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface FooterSocialPlatforms extends Struct.ComponentSchema {
  collectionName: 'components_footer_social_platforms';
  info: {
    displayName: 'SocialPlatforms';
  };
  attributes: {
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    link: Schema.Attribute.String;
  };
}

export interface FooterThirdRow extends Struct.ComponentSchema {
  collectionName: 'components_footer_third_rows';
  info: {
    description: '';
    displayName: 'TermsAndConditionRow';
  };
  attributes: {
    link: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface FormAwardsFormAwards extends Struct.ComponentSchema {
  collectionName: 'components_form_awards_form_awards';
  info: {
    description: '';
    displayName: 'form_awards';
  };
  attributes: {
    images: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface FormCaseStudyForm extends Struct.ComponentSchema {
  collectionName: 'components_form_case_study_forms';
  info: {
    displayName: 'caseStudy_form';
  };
  attributes: {
    description: Schema.Attribute.Text;
    download_title: Schema.Attribute.String;
    form_download_button: Schema.Attribute.Component<'common.button', false>;
    form_values: Schema.Attribute.Component<'form.form-values', false>;
  };
}

export interface FormForm extends Struct.ComponentSchema {
  collectionName: 'components_form_forms';
  info: {
    description: '';
    displayName: 'form';
  };
  attributes: {
    button: Schema.Attribute.Component<'common.button', false>;
    consent_statement: Schema.Attribute.Text;
    formFields: Schema.Attribute.Component<'form.form-fields', false>;
    instructions: Schema.Attribute.Text;
    LinkedInButton_title: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface FormFormFields extends Struct.ComponentSchema {
  collectionName: 'components_form_form_fields';
  info: {
    description: '';
    displayName: 'formFields';
  };
  attributes: {
    fieldNameFor_CompanyName: Schema.Attribute.String;
    fieldNameFor_EmailAddress: Schema.Attribute.String;
    fieldNameFor_FirstName: Schema.Attribute.String;
    fieldNameFor_HowCanWeHelpYou: Schema.Attribute.String;
    fieldNameFor_HowDidYouHearAboutUs: Schema.Attribute.String;
    fieldNameFor_LastName: Schema.Attribute.String;
    fieldNameFor_PhoneNumber: Schema.Attribute.String;
  };
}

export interface FormFormValues extends Struct.ComponentSchema {
  collectionName: 'components_form_form_values';
  info: {
    description: '';
    displayName: 'form_values';
  };
  attributes: {
    fieldNameFor_CompanyName: Schema.Attribute.String;
    fieldNameFor_EmailAddress: Schema.Attribute.String;
    fieldNameFor_FirstName: Schema.Attribute.String;
    fieldNameFor_PhoneNumber: Schema.Attribute.String;
  };
}

export interface HeaderLogo extends Struct.ComponentSchema {
  collectionName: 'components_header_logos';
  info: {
    description: '';
    displayName: 'Logo';
  };
  attributes: {
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    link: Schema.Attribute.String;
  };
}

export interface HeaderMenu1 extends Struct.ComponentSchema {
  collectionName: 'components_header_menu_1s';
  info: {
    description: '';
    displayName: 'Menu_1';
  };
  attributes: {
    button: Schema.Attribute.Component<'common.button-with-image', false>;
    link: Schema.Attribute.String;
    subMenu: Schema.Attribute.Component<'header.submenu', true>;
    title: Schema.Attribute.String;
  };
}

export interface HeaderMenu2 extends Struct.ComponentSchema {
  collectionName: 'components_header_menu_2s';
  info: {
    description: '';
    displayName: 'Menu_2';
  };
  attributes: {
    link: Schema.Attribute.String;
    subLinks: Schema.Attribute.Component<'common.sublinks', true>;
    title: Schema.Attribute.String;
  };
}

export interface HeaderMenu3 extends Struct.ComponentSchema {
  collectionName: 'components_header_menu_3s';
  info: {
    description: '';
    displayName: 'Menu_3';
  };
  attributes: {
    button: Schema.Attribute.Component<'common.button-with-image', false>;
    link: Schema.Attribute.String;
    subLinks: Schema.Attribute.Component<'common.sublinks', true>;
    title: Schema.Attribute.String;
    titleDescription: Schema.Attribute.Component<
      'common.title-description',
      false
    >;
  };
}

export interface HeaderMenu4 extends Struct.ComponentSchema {
  collectionName: 'components_header_menu_4s';
  info: {
    description: '';
    displayName: 'Menu_4';
  };
  attributes: {
    button: Schema.Attribute.Component<'common.button-with-image', false>;
    link: Schema.Attribute.String;
    subLinks: Schema.Attribute.Component<'common.sublinks', true>;
    title: Schema.Attribute.String;
  };
}

export interface HeaderMenu5 extends Struct.ComponentSchema {
  collectionName: 'components_header_menu_5s';
  info: {
    displayName: 'Menu_5';
  };
  attributes: {
    link: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface HeaderSubmenu extends Struct.ComponentSchema {
  collectionName: 'components_header_submenus';
  info: {
    displayName: 'Submenu';
  };
  attributes: {
    link: Schema.Attribute.String;
    sublinks: Schema.Attribute.JSON;
    title: Schema.Attribute.String;
  };
}

export interface HeroSectionAboutUsHeroSection extends Struct.ComponentSchema {
  collectionName: 'components_hero_section_about_us_hero_sections';
  info: {
    displayName: 'AboutUsHeroSection';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    hero_tag: Schema.Attribute.String;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.RichText;
  };
}

export interface HeroSectionHomeHeroSection extends Struct.ComponentSchema {
  collectionName: 'components_home_hero_section_home_hero_sections';
  info: {
    description: '';
    displayName: 'HomeHeroSection';
  };
  attributes: {
    banner_name: Schema.Attribute.String;
    image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    link_url: Schema.Attribute.String & Schema.Attribute.Required;
    open_link_in_new_tab: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<false>;
    service_name: Schema.Attribute.String;
    title_description: Schema.Attribute.Component<
      'common.title-description',
      false
    >;
  };
}

export interface IndustriesCardIndustriesCard extends Struct.ComponentSchema {
  collectionName: 'components_industries_card_industries_cards';
  info: {
    description: '';
    displayName: 'Industries Card';
  };
  attributes: {
    backgroundImage: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    industriesCardsBox: Schema.Attribute.Component<
      'industries-card.industries-cards-box',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface ********************************
  extends Struct.ComponentSchema {
  collectionName: 'components_industries_card_industries_cards_boxes';
  info: {
    description: '';
    displayName: 'industriesCardsBox';
  };
  attributes: {
    backgroundImage: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    button: Schema.Attribute.Component<'common.button', false>;
    description: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
  };
}

export interface InsightsInsights extends Struct.ComponentSchema {
  collectionName: 'components_insights_insights';
  info: {
    description: '';
    displayName: 'Insights';
  };
  attributes: {
    blogs: Schema.Attribute.Relation<'oneToMany', 'api::blog.blog'>;
    circular_text_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    subtitle: Schema.Attribute.RichText;
    taglineUrl: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface InsightsInsightsSlider extends Struct.ComponentSchema {
  collectionName: 'components_insights_insights_sliders';
  info: {
    displayName: 'InsightsSlider';
  };
  attributes: {
    sliderDescription: Schema.Attribute.RichText;
    sliderImage: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    sliderTitle: Schema.Attribute.String;
    viewMoreUrl: Schema.Attribute.String;
  };
}

export interface L2ServicesL2Services extends Struct.ComponentSchema {
  collectionName: 'components_l2_services_l2_services';
  info: {
    displayName: 'L2-Services';
  };
  attributes: {
    L2ServicesCard: Schema.Attribute.Component<
      'other-services.other-services-card',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface MeetOurTeamMeetOurPeople extends Struct.ComponentSchema {
  collectionName: 'components_meet_our_people_meet_our_people';
  info: {
    description: '';
    displayName: 'card_box';
  };
  attributes: {
    background_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    button_link: Schema.Attribute.String;
    button_title: Schema.Attribute.String;
    card_description: Schema.Attribute.RichText;
    card_title: Schema.Attribute.String;
    logo_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
  };
}

export interface MeetOurTeamMeetOurTeam extends Struct.ComponentSchema {
  collectionName: 'components_meet_our_team_meet_our_teams';
  info: {
    description: '';
    displayName: 'Meet-our-team';
  };
  attributes: {
    card_box: Schema.Attribute.Component<'meet-our-team.meet-our-people', true>;
    main_title: Schema.Attribute.String;
  };
}

export interface OtherServicesL3OtherServices extends Struct.ComponentSchema {
  collectionName: 'components_other_services_l3_other_services';
  info: {
    description: '';
    displayName: 'L3_other_services';
  };
  attributes: {
    other_services_card: Schema.Attribute.Component<
      'other-services.other-services-card',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface OtherServicesL3OtherServicesCard
  extends Struct.ComponentSchema {
  collectionName: 'components_other_services_l3_other_services_cards';
  info: {
    description: '';
    displayName: 'l3-other-services-card';
  };
  attributes: {
    other_services_cards: Schema.Attribute.Component<
      'other-services.other-services-card',
      true
    >;
  };
}

export interface OtherServicesOtherServices extends Struct.ComponentSchema {
  collectionName: 'components_other_services_other_services';
  info: {
    description: '';
    displayName: 'other_services';
  };
  attributes: {
    all_services_card_link: Schema.Attribute.String;
    all_services_card_title: Schema.Attribute.String;
    other_services_card: Schema.Attribute.Component<
      'other-services.other-services-card',
      true
    >;
    title: Schema.Attribute.String;
  };
}

export interface OtherServicesOtherServicesCard extends Struct.ComponentSchema {
  collectionName: 'components_other_services_other_services_cards';
  info: {
    description: '';
    displayName: 'other_services_card';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    on_hover_bg_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    service_page_link: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface OurOfficesOurOffices extends Struct.ComponentSchema {
  collectionName: 'components_our_offices_our_offices';
  info: {
    description: '';
    displayName: 'our_offices';
  };
  attributes: {
    india: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
    usa: Schema.Attribute.RichText;
  };
}

export interface OurServicesOurServices extends Struct.ComponentSchema {
  collectionName: 'components_our_services_our_services';
  info: {
    description: '';
    displayName: 'Our-services';
  };
  attributes: {
    ourServicesCard: Schema.Attribute.Component<
      'our-services.our-services-card',
      true
    >;
    subtitle: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
  };
}

export interface OurServicesOurServicesCard extends Struct.ComponentSchema {
  collectionName: 'components_our_services_our_services_cards';
  info: {
    description: '';
    displayName: 'Our-services-card';
  };
  attributes: {
    cardContent: Schema.Attribute.RichText;
    cardImage: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    cardTitle: Schema.Attribute.String;
    cardTitle2: Schema.Attribute.String;
    url: Schema.Attribute.String;
  };
}

export interface PartnersNewsEvents extends Struct.ComponentSchema {
  collectionName: 'components_partners_news_events';
  info: {
    displayName: 'News_events';
  };
  attributes: {
    event_main_pages: Schema.Attribute.Relation<
      'oneToMany',
      'api::event-main-page.event-main-page'
    >;
    news: Schema.Attribute.Relation<'oneToMany', 'api::new.new'>;
  };
}

export interface PodcastPageHeroSection extends Struct.ComponentSchema {
  collectionName: 'components_podcast_page_hero_sections';
  info: {
    description: '';
    displayName: 'hero_section';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
  };
}

export interface PodcastPageLatestEpisode extends Struct.ComponentSchema {
  collectionName: 'components_podcast_page_latest_episodes';
  info: {
    description: '';
    displayName: 'latest_episode';
  };
  attributes: {
    subtitle: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
    video_thumbnail_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    youtube_video_embed_link: Schema.Attribute.String;
  };
}

export interface PodcastPageLinks extends Struct.ComponentSchema {
  collectionName: 'components_podcast_page_links';
  info: {
    description: '';
    displayName: 'links';
  };
  attributes: {
    icon: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    url: Schema.Attribute.String;
  };
}

export interface PodcastPageListenOn extends Struct.ComponentSchema {
  collectionName: 'components_podcast_page_listen_ons';
  info: {
    displayName: 'listen_on';
  };
  attributes: {
    links: Schema.Attribute.Component<'podcast-page.links', true>;
    title: Schema.Attribute.String;
  };
}

export interface PodcastPagePodcastEpisode extends Struct.ComponentSchema {
  collectionName: 'components_podcast_page_podcast_episodes';
  info: {
    description: '';
    displayName: 'podcast_episode';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    episode_listing: Schema.Attribute.String;
    podcast_audio_file: Schema.Attribute.Media<'audios'>;
    spotify_audio_full_url: Schema.Attribute.String;
    title: Schema.Attribute.String;
    video_thumbnail_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    youtube_video_embed_link: Schema.Attribute.String;
  };
}

export interface PodcastPagePodcastSeries extends Struct.ComponentSchema {
  collectionName: 'components_podcast_page_podcast_series';
  info: {
    description: '';
    displayName: 'podcast_series';
  };
  attributes: {
    layout: Schema.Attribute.Enumeration<
      ['Video: Left & Content: Right', 'Content: Left & Video: Right']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'Video: Left & Content: Right'>;
    podcast_episode: Schema.Attribute.Component<
      'podcast-page.podcast-episode',
      true
    >;
    podcast_series_title: Schema.Attribute.String;
    variant: Schema.Attribute.Enumeration<['black', 'white']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'black'>;
  };
}

export interface PrAndNewsCards extends Struct.ComponentSchema {
  collectionName: 'components_pr_and_news_cards';
  info: {
    displayName: 'cards';
  };
  attributes: {
    news: Schema.Attribute.Relation<'oneToMany', 'api::new.new'>;
    prs: Schema.Attribute.Relation<'oneToMany', 'api::pr.pr'>;
  };
}

export interface PrAndNewsPrAndNews extends Struct.ComponentSchema {
  collectionName: 'components_pr_and_news_pr_and_news';
  info: {
    displayName: 'pr_and_news';
  };
  attributes: {
    cards: Schema.Attribute.Component<'pr-and-news.cards', false>;
    title: Schema.Attribute.String;
  };
}

export interface RecognitionAwardsRecognitionsAwards
  extends Struct.ComponentSchema {
  collectionName: 'components_recognition_awards_recognitions_awards';
  info: {
    description: '';
    displayName: 'Recognitions-Awards';
  };
  attributes: {
    awards_box: Schema.Attribute.Component<'common.title-image', true>;
    title: Schema.Attribute.String;
  };
}

export interface ResourcesFilterResourcesFilter extends Struct.ComponentSchema {
  collectionName: 'components_resources_filter_resources_filters';
  info: {
    description: '';
    displayName: 'resources_filter';
  };
  attributes: {
    search_button_title: Schema.Attribute.String;
    select_industries_title: Schema.Attribute.String;
    select_resources_title: Schema.Attribute.String;
    select_services_title: Schema.Attribute.String;
    sort_by_text: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface ResourcesPageSliderResourcesPageSlider
  extends Struct.ComponentSchema {
  collectionName: 'components_resources_page_slider_resources_page_sliders';
  info: {
    description: '';
    displayName: 'resources_page_slider';
  };
  attributes: {
    banner_name: Schema.Attribute.String;
    description: Schema.Attribute.RichText;
    link: Schema.Attribute.String;
    service_name: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface RichTextRichText extends Struct.ComponentSchema {
  collectionName: 'components_rich_text_rich_texts';
  info: {
    description: '';
    displayName: 'rich_text';
  };
  attributes: {
    button: Schema.Attribute.Component<'common.button', false>;
    rich_text: Schema.Attribute.RichText;
    vimeoVideoLink: Schema.Attribute.Text;
  };
}

export interface RichTextRichtextWithTitle extends Struct.ComponentSchema {
  collectionName: 'components_rich_text_richtext_with_titles';
  info: {
    description: '';
    displayName: 'richtext_with_title';
  };
  attributes: {
    background: Schema.Attribute.Enumeration<['black', 'gray']>;
    rich_text: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
  };
}

export interface SearchCustomDataForInitialSearchResults
  extends Struct.ComponentSchema {
  collectionName: 'components_search_custom_data_for_initial_search_results';
  info: {
    description: '';
    displayName: 'custom_data_for_initial_search_results';
  };
  attributes: {
    page_full_url: Schema.Attribute.String & Schema.Attribute.Required;
    page_title: Schema.Attribute.String;
    page_type_title: Schema.Attribute.String;
  };
}

export interface SearchInitialSearchResults extends Struct.ComponentSchema {
  collectionName: 'components_search_initial_search_results';
  info: {
    description: '';
    displayName: 'initial_search_results';
  };
  attributes: {
    blogs: Schema.Attribute.Relation<'oneToMany', 'api::blog.blog'>;
    case_studies: Schema.Attribute.Relation<
      'oneToMany',
      'api::case-study.case-study'
    >;
    custom_data_for_initial_search_results: Schema.Attribute.Component<
      'search.custom-data-for-initial-search-results',
      true
    > &
      Schema.Attribute.SetMinMax<
        {
          max: 5;
        },
        number
      >;
    industries: Schema.Attribute.Relation<
      'oneToMany',
      'api::industry.industry'
    >;
    Instructions_doNotChange: Schema.Attribute.Text & Schema.Attribute.Private;
    l2_service_pages: Schema.Attribute.Relation<
      'oneToMany',
      'api::l2-service-page.l2-service-page'
    >;
    l3_service_pages: Schema.Attribute.Relation<
      'oneToMany',
      'api::l3-service-page.l3-service-page'
    >;
    partners: Schema.Attribute.Relation<'oneToMany', 'api::partner.partner'>;
  };
}

export interface SeoKeywords extends Struct.ComponentSchema {
  collectionName: 'components_seo_keywords';
  info: {
    description: '';
    displayName: 'keywords';
    icon: 'arrow-alt-circle-right';
  };
  attributes: {
    keyword: Schema.Attribute.Text;
  };
}

export interface SeoMetaProperties extends Struct.ComponentSchema {
  collectionName: 'components_seo_meta_properties';
  info: {
    displayName: 'metaProperties';
    icon: 'angle-double-right';
  };
  attributes: {
    content: Schema.Attribute.Text;
    name: Schema.Attribute.String;
  };
}

export interface SeoSeo extends Struct.ComponentSchema {
  collectionName: 'components_seo_seos';
  info: {
    description: '';
    displayName: 'seo';
    icon: 'scroll';
  };
  attributes: {
    description: Schema.Attribute.Text;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    keywords: Schema.Attribute.Component<'seo.keywords', false>;
    locale: Schema.Attribute.String;
    metaProperties: Schema.Attribute.Component<'seo.meta-properties', true>;
    schema: Schema.Attribute.JSON;
    site_name: Schema.Attribute.String;
    title: Schema.Attribute.String;
    type: Schema.Attribute.String;
    url: Schema.Attribute.String;
  };
}

export interface ServiceDeliveryProcessCards extends Struct.ComponentSchema {
  collectionName: 'components_service_delivery_process_cards';
  info: {
    description: '';
    displayName: 'cards';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    number: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface ServiceDeliveryProcessServiceDeliveryProcess
  extends Struct.ComponentSchema {
  collectionName: 'components_service_delivery_process_service_delivery_processes';
  info: {
    displayName: 'service_delivery_process';
  };
  attributes: {
    cards: Schema.Attribute.Component<'service-delivery-process.cards', true>;
    title: Schema.Attribute.String;
  };
}

export interface SolutionPageChallenges extends Struct.ComponentSchema {
  collectionName: 'components_solution_page_challenges';
  info: {
    description: '';
    displayName: 'Challenges';
  };
  attributes: {
    challenges_box: Schema.Attribute.Component<
      'solution-page.challenges-box-slider',
      true
    >;
    description: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
  };
}

export interface SolutionPageChallengesBoxSlider
  extends Struct.ComponentSchema {
  collectionName: 'components_solution_page_challenges_box_sliders';
  info: {
    displayName: 'challenges_box_slider';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
  };
}

export interface TabChallengesTab extends Struct.ComponentSchema {
  collectionName: 'components_tab_challenges_tabs';
  info: {
    displayName: 'tab';
  };
  attributes: {
    card_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    card_title: Schema.Attribute.String;
    challenges_description: Schema.Attribute.RichText;
    challenges_title: Schema.Attribute.String;
    solution_description: Schema.Attribute.RichText;
    solution_title: Schema.Attribute.String;
  };
}

export interface TabChallengesTabChallenges extends Struct.ComponentSchema {
  collectionName: 'components_tab_challenges_tab_challenges';
  info: {
    description: '';
    displayName: 'tab-challenges';
  };
  attributes: {
    tab_box: Schema.Attribute.Component<'tab-challenges.tab', true>;
    title: Schema.Attribute.String;
  };
}

export interface TechStackTab extends Struct.ComponentSchema {
  collectionName: 'components_tech_stack_tabs';
  info: {
    description: '';
    displayName: 'tab';
  };
  attributes: {
    logo_url: Schema.Attribute.JSON;
    tab_title: Schema.Attribute.String;
  };
}

export interface TechStackTechStack extends Struct.ComponentSchema {
  collectionName: 'components_tech_stack_tech_stacks';
  info: {
    displayName: 'tech_stack';
  };
  attributes: {
    tab: Schema.Attribute.Component<'tech-stack.tab', true>;
    title: Schema.Attribute.String;
  };
}

export interface TestimonialsTestimonials extends Struct.ComponentSchema {
  collectionName: 'components_testimonials_testimonials';
  info: {
    description: '';
    displayName: 'Testimonials';
  };
  attributes: {
    circular_text_line_svg: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    tagline_url: Schema.Attribute.String;
    testimonial_playbtn_logo: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    testimonials_slider: Schema.Attribute.Component<
      'common.title-description-image-slider',
      true
    >;
    title: Schema.Attribute.RichText;
  };
}

export interface ThankYouThankYou extends Struct.ComponentSchema {
  collectionName: 'components_thank_you_thank_yous';
  info: {
    description: '';
    displayName: 'Thank-you';
  };
  attributes: {
    button: Schema.Attribute.Component<'common.button', false>;
    description: Schema.Attribute.RichText;
    title: Schema.Attribute.Text;
  };
}

export interface TrustedPartnerTrustedPartner extends Struct.ComponentSchema {
  collectionName: 'components_trusted_partner_trusted_partners';
  info: {
    description: '';
    displayName: 'TrustedPartner';
  };
  attributes: {
    partnersLogo: Schema.Attribute.Component<'common.multiple-image', false>;
    title: Schema.Attribute.String;
  };
}

export interface VideoPageAllVideos extends Struct.ComponentSchema {
  collectionName: 'components_video_page_all_videos';
  info: {
    description: '';
    displayName: 'all_videos';
  };
  attributes: {
    select_video_tags_title: Schema.Attribute.String;
    sort_by_text: Schema.Attribute.String;
    title: Schema.Attribute.String;
  };
}

export interface VisionMissionVisionMission extends Struct.ComponentSchema {
  collectionName: 'components_vision_mission_vision_missions';
  info: {
    description: '';
    displayName: 'Vision-mission';
  };
  attributes: {
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
  };
}

export interface WhyChooseMtlCards extends Struct.ComponentSchema {
  collectionName: 'components_why_choose_mtl_cards';
  info: {
    displayName: 'Cards';
  };
  attributes: {
    cardDescription: Schema.Attribute.RichText;
    cardTitle: Schema.Attribute.RichText;
  };
}

export interface WhyChooseMtlWhyChooseMtl extends Struct.ComponentSchema {
  collectionName: 'components_why_choose_mtl_why_choose_mtls';
  info: {
    description: '';
    displayName: 'Why-choose-MTL';
  };
  attributes: {
    subtitle: Schema.Attribute.RichText;
    title: Schema.Attribute.String;
    whyChooseMtlCards: Schema.Attribute.Component<
      'why-choose-mtl.cards',
      true
    > &
      Schema.Attribute.SetMinMax<
        {
          max: 8;
        },
        number
      >;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'ai-readiness.options': AiReadinessOptions;
      'ai-readiness.question': AiReadinessQuestion;
      'all-service-page.all-service-page': AllServicePageAllServicePage;
      'audit-methodology.audit-methodology': AuditMethodologyAuditMethodology;
      'benefits.benefits': BenefitsBenefits;
      'blog.blog-related-service': BlogBlogRelatedService;
      'blog.case-study-suggestions': BlogCaseStudySuggestions;
      'blog.content': BlogContent;
      'blog.suggestions': BlogSuggestions;
      'careers.core-values': CareersCoreValues;
      'careers.life-at-mtl': CareersLifeAtMtl;
      'case-studies.hero-section': CaseStudiesHeroSection;
      'case-studies.preview': CaseStudiesPreview;
      'case-studies.quote': CaseStudiesQuote;
      'case-study.card-box': CaseStudyCardBox;
      'case-study.case-study': CaseStudyCaseStudy;
      'case-study.case-study-relation': CaseStudyCaseStudyRelation;
      'case-study.filters': CaseStudyFilters;
      'case-study.hero-section': CaseStudyHeroSection;
      'collab.collab': CollabCollab;
      'common.blog-hero': CommonBlogHero;
      'common.button': CommonButton;
      'common.button-with-image': CommonButtonWithImage;
      'common.clutch-reviews': CommonClutchReviews;
      'common.filter': CommonFilter;
      'common.link-box': CommonLinkBox;
      'common.multiple-image': CommonMultipleImage;
      'common.single-image': CommonSingleImage;
      'common.sublinks': CommonSublinks;
      'common.title': CommonTitle;
      'common.title-desc-image-link': CommonTitleDescImageLink;
      'common.title-description': CommonTitleDescription;
      'common.title-description-image': CommonTitleDescriptionImage;
      'common.title-description-image-button': CommonTitleDescriptionImageButton;
      'common.title-description-image-slider': CommonTitleDescriptionImageSlider;
      'common.title-image': CommonTitleImage;
      'common.title-image-repeat': CommonTitleImageRepeat;
      'company-statistics.company-statistics': CompanyStatisticsCompanyStatistics;
      'company-statistics.statistics-cards': CompanyStatisticsStatisticsCards;
      'contact-us-form.contact-us-form': ContactUsFormContactUsForm;
      'contact-us-form.right-side-content': ContactUsFormRightSideContent;
      'cta.cta': CtaCta;
      'ebooks.preview': EbooksPreview;
      'employee-testimonial.employee-box': EmployeeTestimonialEmployeeBox;
      'employee-testimonial.employee-testimonial': EmployeeTestimonialEmployeeTestimonial;
      'events-pages.card': EventsPagesCard;
      'events-pages.hero-section': EventsPagesHeroSection;
      'events-pages.offerings-card': EventsPagesOfferingsCard;
      'events-pages.our-people': EventsPagesOurPeople;
      'events-pages.our-people-card': EventsPagesOurPeopleCard;
      'faq.faq': FaqFaq;
      'faq.faq-items': FaqFaqItems;
      'footer.fourth-row': FooterFourthRow;
      'footer.sector-box': FooterSectorBox;
      'footer.social-platforms': FooterSocialPlatforms;
      'footer.third-row': FooterThirdRow;
      'form-awards.form-awards': FormAwardsFormAwards;
      'form.case-study-form': FormCaseStudyForm;
      'form.form': FormForm;
      'form.form-fields': FormFormFields;
      'form.form-values': FormFormValues;
      'header.logo': HeaderLogo;
      'header.menu-1': HeaderMenu1;
      'header.menu-2': HeaderMenu2;
      'header.menu-3': HeaderMenu3;
      'header.menu-4': HeaderMenu4;
      'header.menu-5': HeaderMenu5;
      'header.submenu': HeaderSubmenu;
      'hero-section.about-us-hero-section': HeroSectionAboutUsHeroSection;
      'hero-section.home-hero-section': HeroSectionHomeHeroSection;
      'industries-card.industries-card': IndustriesCardIndustriesCard;
      'industries-card.industries-cards-box': ********************************;
      'insights.insights': InsightsInsights;
      'insights.insights-slider': InsightsInsightsSlider;
      'l2-services.l2-services': L2ServicesL2Services;
      'meet-our-team.meet-our-people': MeetOurTeamMeetOurPeople;
      'meet-our-team.meet-our-team': MeetOurTeamMeetOurTeam;
      'other-services.l3-other-services': OtherServicesL3OtherServices;
      'other-services.l3-other-services-card': OtherServicesL3OtherServicesCard;
      'other-services.other-services': OtherServicesOtherServices;
      'other-services.other-services-card': OtherServicesOtherServicesCard;
      'our-offices.our-offices': OurOfficesOurOffices;
      'our-services.our-services': OurServicesOurServices;
      'our-services.our-services-card': OurServicesOurServicesCard;
      'partners.news-events': PartnersNewsEvents;
      'podcast-page.hero-section': PodcastPageHeroSection;
      'podcast-page.latest-episode': PodcastPageLatestEpisode;
      'podcast-page.links': PodcastPageLinks;
      'podcast-page.listen-on': PodcastPageListenOn;
      'podcast-page.podcast-episode': PodcastPagePodcastEpisode;
      'podcast-page.podcast-series': PodcastPagePodcastSeries;
      'pr-and-news.cards': PrAndNewsCards;
      'pr-and-news.pr-and-news': PrAndNewsPrAndNews;
      'recognition-awards.recognitions-awards': RecognitionAwardsRecognitionsAwards;
      'resources-filter.resources-filter': ResourcesFilterResourcesFilter;
      'resources-page-slider.resources-page-slider': ResourcesPageSliderResourcesPageSlider;
      'rich-text.rich-text': RichTextRichText;
      'rich-text.richtext-with-title': RichTextRichtextWithTitle;
      'search.custom-data-for-initial-search-results': SearchCustomDataForInitialSearchResults;
      'search.initial-search-results': SearchInitialSearchResults;
      'seo.keywords': SeoKeywords;
      'seo.meta-properties': SeoMetaProperties;
      'seo.seo': SeoSeo;
      'service-delivery-process.cards': ServiceDeliveryProcessCards;
      'service-delivery-process.service-delivery-process': ServiceDeliveryProcessServiceDeliveryProcess;
      'solution-page.challenges': SolutionPageChallenges;
      'solution-page.challenges-box-slider': SolutionPageChallengesBoxSlider;
      'tab-challenges.tab': TabChallengesTab;
      'tab-challenges.tab-challenges': TabChallengesTabChallenges;
      'tech-stack.tab': TechStackTab;
      'tech-stack.tech-stack': TechStackTechStack;
      'testimonials.testimonials': TestimonialsTestimonials;
      'thank-you.thank-you': ThankYouThankYou;
      'trusted-partner.trusted-partner': TrustedPartnerTrustedPartner;
      'video-page.all-videos': VideoPageAllVideos;
      'vision-mission.vision-mission': VisionMissionVisionMission;
      'why-choose-mtl.cards': WhyChooseMtlCards;
      'why-choose-mtl.why-choose-mtl': WhyChooseMtlWhyChooseMtl;
    }
  }
}
