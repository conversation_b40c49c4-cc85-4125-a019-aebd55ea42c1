export default () => ({
  // Temporarily disabled CKEditor - needs v5 configuration update
  // Will be reconfigured later with proper v5 syntax

  // AWS S3 Upload Configuration (Private Bucket + CloudFront)
  upload: {
    config: {
      provider: "@strapi/provider-upload-aws-s3",
      providerOptions: {
        s3Options: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
          region: 'ap-south-1',
          params: {
            // No ACL specified - bucket has public access blocked
            signedUrlExpires: 15 * 60, // 15 minutes for admin operations
            Bucket: "maruti-site-cdn",
          },
        },
        // CloudFront distribution URL for public file access
        baseUrl: process.env.CLOUDFRONT_URL,
      },
    },
  },
  // Temporarily disabled - not compatible with Strapi v5 yet
  // "import-export-entries": {
  //   enabled: false,
  //   resolve: "./src/plugins/import-export",
  //   config: {
  //     // See `Config` section.
  //   },
  // },
  // Temporarily disabled - needs v5 compatibility update
  // "publish-to-staging": {
  //   enabled: true,
  //   resolve: "./src/plugins/publish-to-staging",
  // },
  // Removed content-preview plugin - will be replaced with Strapi v5 native preview functionality
});
