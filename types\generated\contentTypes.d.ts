import type { Schema, Struct } from '@strapi/strapi';

export interface AdminApiToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_tokens';
  info: {
    description: '';
    displayName: 'Api Token';
    name: 'Api Token';
    pluralName: 'api-tokens';
    singularName: 'api-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    encryptedKey: Schema.Attribute.Text &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::api-token'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'read-only'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_token_permissions';
  info: {
    description: '';
    displayName: 'API Token Permission';
    name: 'API Token Permission';
    pluralName: 'api-token-permissions';
    singularName: 'api-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::api-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
  collectionName: 'admin_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'Permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::permission'> &
      Schema.Attribute.Private;
    properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<'manyToOne', 'admin::role'>;
    subject: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminRole extends Struct.CollectionTypeSchema {
  collectionName: 'admin_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'Role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::role'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>;
  };
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_tokens';
  info: {
    description: '';
    displayName: 'Transfer Token';
    name: 'Transfer Token';
    pluralName: 'transfer-tokens';
    singularName: 'transfer-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminTransferTokenPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    description: '';
    displayName: 'Transfer Token Permission';
    name: 'Transfer Token Permission';
    pluralName: 'transfer-token-permissions';
    singularName: 'transfer-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::transfer-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminUser extends Struct.CollectionTypeSchema {
  collectionName: 'admin_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'User';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    blocked: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    isActive: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    lastname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::user'> &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    preferedLanguage: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    roles: Schema.Attribute.Relation<'manyToMany', 'admin::role'> &
      Schema.Attribute.Private;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String;
  };
}

export interface ApiAboutUsAboutUs extends Struct.SingleTypeSchema {
  collectionName: 'about_uses';
  info: {
    description: '';
    displayName: 'About Us';
    pluralName: 'about-uses';
    singularName: 'about-us';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    build_careers: Schema.Attribute.Component<
      'common.title-image-repeat',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    download_our_brand: Schema.Attribute.Component<'cta.cta', false>;
    featured_on: Schema.Attribute.Component<
      'trusted-partner.trusted-partner',
      false
    >;
    hero_section: Schema.Attribute.Component<
      'hero-section.about-us-hero-section',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::about-us.about-us'
    > &
      Schema.Attribute.Private;
    meet_our_people: Schema.Attribute.Component<
      'events-pages.our-people',
      false
    >;
    our_story: Schema.Attribute.Component<
      'service-delivery-process.service-delivery-process',
      false
    >;
    pr_and_news: Schema.Attribute.Component<'pr-and-news.pr-and-news', false>;
    publishedAt: Schema.Attribute.DateTime;
    rich_text: Schema.Attribute.Component<'rich-text.rich-text', false>;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    services_delivery_process: Schema.Attribute.Component<
      'common.title-image',
      false
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    vision_mission: Schema.Attribute.Component<
      'vision-mission.vision-mission',
      true
    >;
  };
}

export interface ApiAiReadinessComponentAiReadinessComponent
  extends Struct.CollectionTypeSchema {
  collectionName: 'ai_readiness_components';
  info: {
    description: '';
    displayName: 'AI Readiness Component';
    pluralName: 'ai-readiness-components';
    singularName: 'ai-readiness-component';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    heading: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::ai-readiness-component.ai-readiness-component'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    question: Schema.Attribute.Component<'ai-readiness.question', true>;
    section_weight: Schema.Attribute.Decimal;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiAiReadinessAiReadiness extends Struct.SingleTypeSchema {
  collectionName: 'ai_readinesses';
  info: {
    description: '';
    displayName: 'AI Readiness';
    pluralName: 'ai-readinesses';
    singularName: 'ai-readiness';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    ai_readiness_components: Schema.Attribute.Relation<
      'oneToMany',
      'api::ai-readiness-component.ai-readiness-component'
    >;
    consultation_button: Schema.Attribute.Component<'common.button', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    form: Schema.Attribute.Component<'form.form', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image-button',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::ai-readiness.ai-readiness'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    restart_button: Schema.Attribute.Component<'common.button', false>;
    score_heading: Schema.Attribute.String;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    tag: Schema.Attribute.Component<'common.title-description', false>;
    tag_list: Schema.Attribute.Component<'ai-readiness.options', true>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiAllResourcesPageAllResourcesPage
  extends Struct.SingleTypeSchema {
  collectionName: 'all_resources_pages';
  info: {
    description: '';
    displayName: 'All resources page';
    pluralName: 'all-resources-pages';
    singularName: 'all-resources-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    filter_ui: Schema.Attribute.Component<
      'resources-filter.resources-filter',
      false
    >;
    hero_section: Schema.Attribute.Component<'common.title-image', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::all-resources-page.all-resources-page'
    > &
      Schema.Attribute.Private;
    page_name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    resources_silder: Schema.Attribute.Component<
      'resources-page-slider.resources-page-slider',
      true
    >;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiAllServicePageAllServicePage
  extends Struct.SingleTypeSchema {
  collectionName: 'all_service_pages';
  info: {
    description: '';
    displayName: 'all_service_page';
    pluralName: 'all-service-pages';
    singularName: 'all-service-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    all_service_page: Schema.Attribute.Component<
      'all-service-page.all-service-page',
      true
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::all-service-page.all-service-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    richtext_with_title: Schema.Attribute.Component<
      'rich-text.richtext-with-title',
      false
    >;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiAuthorAuthor extends Struct.CollectionTypeSchema {
  collectionName: 'authors';
  info: {
    description: '';
    displayName: 'author';
    pluralName: 'authors';
    singularName: 'author';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    blogs: Schema.Attribute.Relation<'manyToMany', 'api::blog.blog'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.RichText;
    designation: Schema.Attribute.String;
    image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios',
      true
    >;
    linkedin_link: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::author.author'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String;
    twitter_link: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiAwardAward extends Struct.SingleTypeSchema {
  collectionName: 'awards';
  info: {
    displayName: 'Award';
    pluralName: 'awards';
    singularName: 'award';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    awards: Schema.Attribute.Component<
      'recognition-awards.recognitions-awards',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::award.award'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBlogListingPageBlogListingPage
  extends Struct.SingleTypeSchema {
  collectionName: 'blog_listing_pages';
  info: {
    description: '';
    displayName: 'Blog Listing Page';
    pluralName: 'blog-listing-pages';
    singularName: 'blog-listing-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    filter: Schema.Attribute.Component<'case-study.filters', false>;
    hero_section: Schema.Attribute.Component<'case-study.hero-section', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::blog-listing-page.blog-listing-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBlogBlog extends Struct.CollectionTypeSchema {
  collectionName: 'blogs';
  info: {
    description: '';
    displayName: 'blogs';
    pluralName: 'blogs';
    singularName: 'blog';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    audio_file: Schema.Attribute.Media<'audios'>;
    authors: Schema.Attribute.Relation<'manyToMany', 'api::author.author'>;
    blog_related_service: Schema.Attribute.Component<
      'blog.blog-related-service',
      false
    >;
    caseStudy_suggestions: Schema.Attribute.Component<
      'blog.case-study-suggestions',
      false
    >;
    content: Schema.Attribute.Component<'blog.content', true>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.Text & Schema.Attribute.Required;
    global_industries: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-industry.global-industry'
    >;
    global_services: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-service.global-service'
    >;
    heroSection_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    > &
      Schema.Attribute.Required;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'> &
      Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::blog.blog'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    suggestions: Schema.Attribute.Component<'blog.suggestions', false>;
    title: Schema.Attribute.String & Schema.Attribute.Required;
    type: Schema.Attribute.Enumeration<
      [
        'Agile',
        'Artificial Intelligence and Machine Learning',
        'Block Chain',
        'Bot Development',
        'Business Strategy',
        'Chatbot',
        'Cloud',
        'Data Analytics and Business Intelligence',
        'Devops',
        'Low Code No Code Development',
        'Product Development',
        'QA',
        'Robotic Process Automation',
        'Salesforce Development',
        'Software Development Practices',
        'Achievements',
        'User Experience',
      ]
    > &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCareerCareer extends Struct.SingleTypeSchema {
  collectionName: 'careers';
  info: {
    description: '';
    displayName: 'Career';
    pluralName: 'careers';
    singularName: 'career';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    benefits: Schema.Attribute.Component<'benefits.benefits', false>;
    core_values: Schema.Attribute.Component<'careers.core-values', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    employee_testimonial: Schema.Attribute.Component<
      'employee-testimonial.employee-testimonial',
      false
    >;
    gptw: Schema.Attribute.Component<'common.title-description-image', false>;
    hero_section: Schema.Attribute.Component<'rich-text.rich-text', false>;
    life_at_mtl: Schema.Attribute.Component<'careers.life-at-mtl', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::career.career'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCaseStudyListingPageCaseStudyListingPage
  extends Struct.SingleTypeSchema {
  collectionName: 'case_study_listing_pages';
  info: {
    description: '';
    displayName: 'Case Study Listing Page';
    pluralName: 'case-study-listing-pages';
    singularName: 'case-study-listing-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    filter: Schema.Attribute.Component<'case-study.filters', false>;
    hero_section: Schema.Attribute.Component<'case-study.hero-section', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::case-study-listing-page.case-study-listing-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCaseStudyCaseStudy extends Struct.CollectionTypeSchema {
  collectionName: 'case_studies';
  info: {
    description: '';
    displayName: 'Case Studies';
    pluralName: 'case-studies';
    singularName: 'case-study';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    caseStudy_form: Schema.Attribute.Component<'form.case-study-form', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    global_resource_type: Schema.Attribute.Relation<
      'oneToOne',
      'api::global-resource-type.global-resource-type'
    >;
    hero_section: Schema.Attribute.Component<
      'case-studies.hero-section',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::case-study.case-study'
    > &
      Schema.Attribute.Private;
    preview: Schema.Attribute.Component<'case-studies.preview', false>;
    publishedAt: Schema.Attribute.DateTime;
    quote: Schema.Attribute.Component<'case-studies.quote', false>;
    richText_for_Challenges: Schema.Attribute.Component<
      'rich-text.richtext-with-title',
      false
    >;
    richText_for_Results: Schema.Attribute.Component<
      'rich-text.richtext-with-title',
      false
    >;
    richText_for_Solutions: Schema.Attribute.Component<
      'rich-text.richtext-with-title',
      false
    >;
    richText_for_TheClient: Schema.Attribute.Component<
      'rich-text.richtext-with-title',
      false
    >;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCityServicePageCityServicePage
  extends Struct.CollectionTypeSchema {
  collectionName: 'city_service_pages';
  info: {
    description: '';
    displayName: 'CityServicePage';
    pluralName: 'city-service-pages';
    singularName: 'city-service-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    audit_review: Schema.Attribute.Component<'common.title-description', false>;
    case_study_cards: Schema.Attribute.Component<
      'case-study.case-study-relation',
      false
    >;
    challenges: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cta: Schema.Attribute.Component<'cta.cta', false>;
    cta_2: Schema.Attribute.Component<'cta.cta', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image',
      false
    >;
    insights: Schema.Attribute.Component<'insights.insights', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::city-service-page.city-service-page'
    > &
      Schema.Attribute.Private;
    other_services: Schema.Attribute.Component<
      'other-services.l3-other-services',
      false
    >;
    our_audit_methodology: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    our_service_delivery_process: Schema.Attribute.Component<
      'service-delivery-process.service-delivery-process',
      false
    >;
    page_name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    service_offering_card: Schema.Attribute.Component<
      'l2-services.l2-services',
      false
    >;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    tech_stack: Schema.Attribute.Component<'tech-stack.tech-stack', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    whyChooseMtl: Schema.Attribute.Component<
      'why-choose-mtl.why-choose-mtl',
      false
    >;
  };
}

export interface ApiCloudConsultingCloudConsulting
  extends Struct.CollectionTypeSchema {
  collectionName: 'cloud_consultings';
  info: {
    description: '';
    displayName: 'Cloud Consulting';
    pluralName: 'cloud-consultings';
    singularName: 'cloud-consulting';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    audit_button: Schema.Attribute.Component<'common.button', false>;
    challenges: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cta: Schema.Attribute.Component<'cta.cta', false>;
    deliverables: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    faq: Schema.Attribute.Component<'faq.faq', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-desc-image-link',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cloud-consulting.cloud-consulting'
    > &
      Schema.Attribute.Private;
    our_audit_methodology: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    pageName: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    scope_and_deliverables: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    solution: Schema.Attribute.Component<'common.title-description', false>;
    solution_with_video: Schema.Attribute.Component<
      'podcast-page.podcast-series',
      false
    >;
    tech_stack: Schema.Attribute.Component<'tech-stack.tech-stack', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    why_choose_mtl: Schema.Attribute.Component<
      'why-choose-mtl.why-choose-mtl',
      false
    >;
  };
}

export interface ApiCloudMigrationComponentCloudMigrationComponent
  extends Struct.CollectionTypeSchema {
  collectionName: 'cloud_migration_components';
  info: {
    description: '';
    displayName: 'Cloud Migration Component';
    pluralName: 'cloud-migration-components';
    singularName: 'cloud-migration-component';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    heading: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cloud-migration-component.cloud-migration-component'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    questions: Schema.Attribute.Component<'ai-readiness.question', true>;
    section_cost: Schema.Attribute.BigInteger;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCloudMigrationCostCalculatorCloudMigrationCostCalculator
  extends Struct.SingleTypeSchema {
  collectionName: 'cloud_migration_cost_calculators';
  info: {
    description: '';
    displayName: 'Cloud Migration Cost Calculator';
    pluralName: 'cloud-migration-cost-calculators';
    singularName: 'cloud-migration-cost-calculator';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    cloud_migration_components: Schema.Attribute.Relation<
      'oneToMany',
      'api::cloud-migration-component.cloud-migration-component'
    >;
    cost_range_heading: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    form: Schema.Attribute.Component<'form.form', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image-button',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cloud-migration-cost-calculator.cloud-migration-cost-calculator'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    restart_button: Schema.Attribute.Component<'common.button', false>;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    tag: Schema.Attribute.Component<'common.title-description', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiContactUsContactUs extends Struct.SingleTypeSchema {
  collectionName: 'contact_uses';
  info: {
    description: '';
    displayName: 'contact_us';
    pluralName: 'contact-uses';
    singularName: 'contact-us';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    left_section_content: Schema.Attribute.Component<
      'contact-us-form.contact-us-form',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::contact-us.contact-us'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    right_section_content: Schema.Attribute.Component<
      'contact-us-form.right-side-content',
      false
    >;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCookiePolicyCookiePolicy extends Struct.SingleTypeSchema {
  collectionName: 'cookie_policies';
  info: {
    displayName: 'Cookie-policy';
    pluralName: 'cookie-policies';
    singularName: 'cookie-policy';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cookie-policy.cookie-policy'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    rich_text: Schema.Attribute.RichText;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEBookEBook extends Struct.CollectionTypeSchema {
  collectionName: 'e_books';
  info: {
    description: '';
    displayName: 'eBook';
    pluralName: 'e-books';
    singularName: 'e-book';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    form: Schema.Attribute.Component<'form.case-study-form', false>;
    global_resource_type: Schema.Attribute.Relation<
      'oneToOne',
      'api::global-resource-type.global-resource-type'
    >;
    hero_section: Schema.Attribute.Component<'case-study.hero-section', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::e-book.e-book'
    > &
      Schema.Attribute.Private;
    preview: Schema.Attribute.Component<'ebooks.preview', false>;
    publishedAt: Schema.Attribute.DateTime;
    rich_text: Schema.Attribute.RichText;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEBooksListingPageEBooksListingPage
  extends Struct.SingleTypeSchema {
  collectionName: 'e_books_listing_pages';
  info: {
    description: '';
    displayName: 'eBooks Listing Page';
    pluralName: 'e-books-listing-pages';
    singularName: 'e-books-listing-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    hero_section: Schema.Attribute.Component<'case-study.hero-section', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::e-books-listing-page.e-books-listing-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEventListingPageEventListingPage
  extends Struct.SingleTypeSchema {
  collectionName: 'event_listing_pages';
  info: {
    description: '';
    displayName: 'Event Listing Page';
    pluralName: 'event-listing-pages';
    singularName: 'event-listing-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    form: Schema.Attribute.Component<'form.form', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::event-listing-page.event-listing-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiEventMainPageEventMainPage
  extends Struct.CollectionTypeSchema {
  collectionName: 'event_main_pages';
  info: {
    description: '';
    displayName: 'Event Main Page';
    pluralName: 'event-main-pages';
    singularName: 'event-main-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    about_event: Schema.Attribute.Component<
      'rich-text.richtext-with-title',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    global_resource_type: Schema.Attribute.Relation<
      'oneToOne',
      'api::global-resource-type.global-resource-type'
    >;
    hero_section: Schema.Attribute.Component<
      'events-pages.hero-section',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::event-main-page.event-main-page'
    > &
      Schema.Attribute.Private;
    meet_our_people: Schema.Attribute.Component<
      'events-pages.our-people',
      false
    >;
    offerings: Schema.Attribute.Component<'events-pages.offerings-card', false>;
    page_name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    why_choose_mtl: Schema.Attribute.Component<
      'why-choose-mtl.why-choose-mtl',
      false
    >;
  };
}

export interface ApiFooterFooter extends Struct.SingleTypeSchema {
  collectionName: 'footers';
  info: {
    description: '';
    displayName: 'Footer';
    pluralName: 'footers';
    singularName: 'footer';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    company_logo_section: Schema.Attribute.Component<
      'footer.fourth-row',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::footer.footer'
    > &
      Schema.Attribute.Private;
    pages_row: Schema.Attribute.Component<'common.link-box', true>;
    publishedAt: Schema.Attribute.DateTime;
    sector_row: Schema.Attribute.Component<'common.link-box', true>;
    terms_and_condition_section: Schema.Attribute.Component<
      'footer.third-row',
      true
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFormForm extends Struct.SingleTypeSchema {
  collectionName: 'forms';
  info: {
    displayName: 'Form';
    pluralName: 'forms';
    singularName: 'form';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    form: Schema.Attribute.Component<'form.form', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::form.form'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGlobalIndustryGlobalIndustry
  extends Struct.CollectionTypeSchema {
  collectionName: 'global_industries';
  info: {
    displayName: 'Global Industry';
    pluralName: 'global-industries';
    singularName: 'global-industry';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    industry_title: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-industry.global-industry'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGlobalResourceTypeGlobalResourceType
  extends Struct.CollectionTypeSchema {
  collectionName: 'global_resource_types';
  info: {
    displayName: 'Global Resource Type';
    pluralName: 'global-resource-types';
    singularName: 'global-resource-type';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-resource-type.global-resource-type'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    resource_type_title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGlobalServiceGlobalService
  extends Struct.CollectionTypeSchema {
  collectionName: 'global_services';
  info: {
    displayName: 'Global Service';
    pluralName: 'global-services';
    singularName: 'global-service';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-service.global-service'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    service_title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGlobalVideoTagGlobalVideoTag
  extends Struct.CollectionTypeSchema {
  collectionName: 'global_video_tags';
  info: {
    displayName: 'Global Video Tag';
    pluralName: 'global-video-tags';
    singularName: 'global-video-tag';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::global-video-tag.global-video-tag'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    tag: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiHeaderHeader extends Struct.SingleTypeSchema {
  collectionName: 'headers';
  info: {
    description: '';
    displayName: 'Header';
    pluralName: 'headers';
    singularName: 'header';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::header.header'
    > &
      Schema.Attribute.Private;
    logo: Schema.Attribute.Component<'header.logo', false>;
    menu: Schema.Attribute.DynamicZone<
      [
        'header.menu-1',
        'header.menu-2',
        'header.menu-3',
        'header.menu-4',
        'header.menu-5',
      ]
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiHomePageHomePage extends Struct.SingleTypeSchema {
  collectionName: 'home_pages';
  info: {
    description: '';
    displayName: 'HomePage';
    pluralName: 'home-pages';
    singularName: 'home-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    case_study_cards: Schema.Attribute.Component<
      'case-study.case-study-relation',
      false
    >;
    Company_Statistics: Schema.Attribute.Component<
      'company-statistics.company-statistics',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    hero_section: Schema.Attribute.Component<
      'hero-section.home-hero-section',
      true
    >;
    Industries: Schema.Attribute.Component<
      'industries-card.industries-card',
      false
    >;
    insights: Schema.Attribute.Component<'insights.insights', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::home-page.home-page'
    > &
      Schema.Attribute.Private;
    our_services: Schema.Attribute.Component<
      'our-services.our-services',
      false
    >;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiIndustryIndustry extends Struct.CollectionTypeSchema {
  collectionName: 'industries';
  info: {
    description: '';
    displayName: 'Industry';
    pluralName: 'industries';
    singularName: 'industry';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    case_study_cards: Schema.Attribute.Component<
      'case-study.case-study-relation',
      false
    >;
    challenges_and_solutions: Schema.Attribute.Component<
      'tab-challenges.tab-challenges',
      false
    >;
    clutch_reviews: Schema.Attribute.Component<'common.clutch-reviews', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    CTA: Schema.Attribute.Component<'cta.cta', false>;
    CTA2: Schema.Attribute.Component<'cta.cta', false>;
    faq: Schema.Attribute.Component<'faq.faq', false>;
    form: Schema.Attribute.Component<'form.form', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image',
      false
    >;
    industry_awards: Schema.Attribute.Component<
      'recognition-awards.recognitions-awards',
      false
    >;
    insights: Schema.Attribute.Component<'insights.insights', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::industry.industry'
    > &
      Schema.Attribute.Private;
    pageName: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    tech_stack: Schema.Attribute.Component<'tech-stack.tech-stack', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    what_service_we_are_offering: Schema.Attribute.Component<
      'l2-services.l2-services',
      false
    >;
    why_choose_maruti_techlabs: Schema.Attribute.Component<
      'why-choose-mtl.why-choose-mtl',
      false
    >;
  };
}

export interface ApiL2ServicePageL2ServicePage
  extends Struct.CollectionTypeSchema {
  collectionName: 'l2_service_pages';
  info: {
    description: '';
    displayName: 'L2ServicePages';
    pluralName: 'l2-service-pages';
    singularName: 'l2-service-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    case_study_cards: Schema.Attribute.Component<
      'case-study.case-study-relation',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cta: Schema.Attribute.Component<'cta.cta', false>;
    cta_1: Schema.Attribute.Component<'cta.cta', false>;
    cta_other: Schema.Attribute.Component<'cta.cta', false>;
    explicit_model_expertise: Schema.Attribute.Component<
      'tech-stack.tech-stack',
      false
    >;
    faq: Schema.Attribute.Component<'faq.faq', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image',
      false
    >;
    insights: Schema.Attribute.Component<'insights.insights', false>;
    l_3_service_pages: Schema.Attribute.Relation<
      'oneToMany',
      'api::l3-service-page.l3-service-page'
    >;
    layout_type: Schema.Attribute.Enumeration<['layout_1', 'layout_2']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'layout_1'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::l2-service-page.l2-service-page'
    > &
      Schema.Attribute.Private;
    other_services: Schema.Attribute.Component<
      'other-services.other-services',
      false
    >;
    our_audit_methodology: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    pageName: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    scope_and_deliverables: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    ServiceOfferingCard: Schema.Attribute.Component<
      'l2-services.l2-services',
      false
    >;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    tech_stack: Schema.Attribute.Component<'tech-stack.tech-stack', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiL3ServicePageL3ServicePage
  extends Struct.CollectionTypeSchema {
  collectionName: 'l3_services_pages';
  info: {
    description: '';
    displayName: 'L3ServicePages';
    pluralName: 'l3-services-pages';
    singularName: 'l3-service-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    case_study_cards: Schema.Attribute.Component<
      'case-study.case-study-relation',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cta: Schema.Attribute.Component<'cta.cta', false>;
    cta_2: Schema.Attribute.Component<'cta.cta', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image',
      false
    >;
    insights: Schema.Attribute.Component<'insights.insights', false>;
    l_2_service_page: Schema.Attribute.Relation<
      'manyToOne',
      'api::l2-service-page.l2-service-page'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::l3-service-page.l3-service-page'
    > &
      Schema.Attribute.Private;
    other_services: Schema.Attribute.Component<
      'other-services.l3-other-services',
      false
    >;
    our_service_delivery_process: Schema.Attribute.Component<
      'service-delivery-process.service-delivery-process',
      false
    >;
    pageName: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    service_offering_card: Schema.Attribute.Component<
      'l2-services.l2-services',
      false
    >;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    tech_stack: Schema.Attribute.Component<'tech-stack.tech-stack', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    whyChooseMtl: Schema.Attribute.Component<
      'why-choose-mtl.why-choose-mtl',
      false
    >;
  };
}

export interface ApiNewNew extends Struct.CollectionTypeSchema {
  collectionName: 'news';
  info: {
    description: '';
    displayName: 'News';
    pluralName: 'news';
    singularName: 'new';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    date: Schema.Attribute.String;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::new.new'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPartnerPartner extends Struct.CollectionTypeSchema {
  collectionName: 'partners';
  info: {
    description: '';
    displayName: 'Partner';
    pluralName: 'partners';
    singularName: 'partner';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    case_study_cards: Schema.Attribute.Component<
      'case-study.case-study-relation',
      false
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cta: Schema.Attribute.Component<'cta.cta', false>;
    cta_other: Schema.Attribute.Component<'cta.cta', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-desc-image-link',
      false
    >;
    Industries: Schema.Attribute.Component<
      'industries-card.industries-card',
      false
    >;
    insights: Schema.Attribute.Component<'insights.insights', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::partner.partner'
    > &
      Schema.Attribute.Private;
    meet_our_people: Schema.Attribute.Component<
      'events-pages.our-people',
      false
    >;
    news_and_events: Schema.Attribute.Component<'partners.news-events', false>;
    page_name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    service_offering: Schema.Attribute.Component<
      'l2-services.l2-services',
      false
    >;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPodcastPagePodcastPage extends Struct.SingleTypeSchema {
  collectionName: 'podcast_pages';
  info: {
    description: '';
    displayName: 'Podcast Page';
    pluralName: 'podcast-pages';
    singularName: 'podcast-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    CTA: Schema.Attribute.Component<'cta.cta', false>;
    hero_section: Schema.Attribute.Component<
      'podcast-page.hero-section',
      false
    >;
    latest_episode: Schema.Attribute.Component<
      'podcast-page.latest-episode',
      false
    >;
    listen_on: Schema.Attribute.Component<'podcast-page.listen-on', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::podcast-page.podcast-page'
    > &
      Schema.Attribute.Private;
    play_button_across_page: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    podcast_series: Schema.Attribute.Component<
      'podcast-page.podcast-series',
      true
    >;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPrPr extends Struct.CollectionTypeSchema {
  collectionName: 'prs';
  info: {
    description: '';
    displayName: 'PR';
    pluralName: 'prs';
    singularName: 'pr';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    date: Schema.Attribute.String;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::pr.pr'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPrivacyPolicyPrivacyPolicy extends Struct.SingleTypeSchema {
  collectionName: 'privacy_policies';
  info: {
    description: '';
    displayName: 'Privacy-policy';
    pluralName: 'privacy-policies';
    singularName: 'privacy-policy';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::privacy-policy.privacy-policy'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    rich_text: Schema.Attribute.RichText;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiRetailComponentRetailComponent
  extends Struct.CollectionTypeSchema {
  collectionName: 'retail_components';
  info: {
    description: '';
    displayName: 'RetailComponent';
    pluralName: 'retail-components';
    singularName: 'retail-component';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    case_study_cards: Schema.Attribute.Component<
      'case-study.case-study-relation',
      false
    >;
    challenges_and_solutions: Schema.Attribute.Component<
      'audit-methodology.audit-methodology',
      false
    >;
    clutch_reviews: Schema.Attribute.Component<'common.clutch-reviews', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cta: Schema.Attribute.Component<'cta.cta', false>;
    cta_other: Schema.Attribute.Component<'cta.cta', false>;
    faq: Schema.Attribute.Component<'faq.faq', false>;
    insights: Schema.Attribute.Component<'insights.insights', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::retail-component.retail-component'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    tab_title: Schema.Attribute.String;
    tech_stack: Schema.Attribute.Component<'tech-stack.tech-stack', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    what_service_we_are_offering: Schema.Attribute.Component<
      'l2-services.l2-services',
      false
    >;
    why_choose_maruti_techlabs: Schema.Attribute.Component<
      'why-choose-mtl.why-choose-mtl',
      false
    >;
  };
}

export interface ApiRetailPageRetailPage extends Struct.CollectionTypeSchema {
  collectionName: 'retail_pages';
  info: {
    description: '';
    displayName: 'RetailPage';
    pluralName: 'retail-pages';
    singularName: 'retail-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::retail-page.retail-page'
    > &
      Schema.Attribute.Private;
    pageName: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    retail_components: Schema.Attribute.Relation<
      'oneToMany',
      'api::retail-component.retail-component'
    >;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    tab_section: Schema.Attribute.Component<'common.title-description', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSearchSearch extends Struct.SingleTypeSchema {
  collectionName: 'searches';
  info: {
    description: '';
    displayName: 'Search';
    pluralName: 'searches';
    singularName: 'search';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    all_results_title: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    initial_search_results: Schema.Attribute.Component<
      'search.initial-search-results',
      false
    > &
      Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::search.search'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    search_input_placeholder: Schema.Attribute.String;
    search_input_title: Schema.Attribute.String;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    showing_top_20_results_title: Schema.Attribute.RichText;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSolutionSolution extends Struct.CollectionTypeSchema {
  collectionName: 'solutions';
  info: {
    description: '';
    displayName: 'Solution';
    pluralName: 'solutions';
    singularName: 'solution';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    business_use_cases: Schema.Attribute.Component<
      'solution-page.challenges',
      false
    >;
    case_study_cards: Schema.Attribute.Component<
      'case-study.case-study-relation',
      false
    >;
    challenges: Schema.Attribute.Component<'solution-page.challenges', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cta: Schema.Attribute.Component<'cta.cta', false>;
    hero_section: Schema.Attribute.Component<
      'common.title-description-image',
      false
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::solution.solution'
    > &
      Schema.Attribute.Private;
    page_name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    solution: Schema.Attribute.Component<'common.title-description', false>;
    tech_stack: Schema.Attribute.Component<'tech-stack.tech-stack', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    why_choose_mtl: Schema.Attribute.Component<
      'why-choose-mtl.why-choose-mtl',
      false
    >;
  };
}

export interface ApiTestTest extends Struct.CollectionTypeSchema {
  collectionName: 'tests';
  info: {
    description: '';
    displayName: 'test';
    pluralName: 'tests';
    singularName: 'test';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.RichText;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    kuldip: Schema.Attribute.RichText;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::test.test'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTestimonialTestimonial extends Struct.SingleTypeSchema {
  collectionName: 'testimonials';
  info: {
    description: '';
    displayName: 'testimonial';
    pluralName: 'testimonials';
    singularName: 'testimonial';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::testimonial.testimonial'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    testimonials: Schema.Attribute.Component<
      'testimonials.testimonials',
      false
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiThankYouThankYou extends Struct.SingleTypeSchema {
  collectionName: 'thank_yous';
  info: {
    description: '';
    displayName: 'Thank You';
    pluralName: 'thank-yous';
    singularName: 'thank-you';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::thank-you.thank-you'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    thank_you: Schema.Attribute.Component<'thank-you.thank-you', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTrustedPartnerTrustedPartner
  extends Struct.SingleTypeSchema {
  collectionName: 'trusted_partners';
  info: {
    description: '';
    displayName: 'TrustedPartner';
    pluralName: 'trusted-partners';
    singularName: 'trusted-partner';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::trusted-partner.trusted-partner'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    trustedPartner: Schema.Attribute.Component<
      'trusted-partner.trusted-partner',
      false
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiVideoPageVideoPage extends Struct.SingleTypeSchema {
  collectionName: 'video_pages';
  info: {
    description: '';
    displayName: 'video_page';
    pluralName: 'video-pages';
    singularName: 'video-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    all_videos: Schema.Attribute.Component<'video-page.all-videos', false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    hero_section: Schema.Attribute.Component<'common.title-image', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::video-page.video-page'
    > &
      Schema.Attribute.Private;
    play_button_across_page: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiVideoVideo extends Struct.CollectionTypeSchema {
  collectionName: 'videos';
  info: {
    description: '';
    displayName: 'video';
    pluralName: 'videos';
    singularName: 'video';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    global_video_tag: Schema.Attribute.Relation<
      'oneToOne',
      'api::global-video-tag.global-video-tag'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::video.video'> &
      Schema.Attribute.Private;
    publication_date: Schema.Attribute.Date;
    publishedAt: Schema.Attribute.DateTime;
    thumbnail: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String;
  };
}

export interface ApiWhitePaperWhitePaper extends Struct.CollectionTypeSchema {
  collectionName: 'white_papers';
  info: {
    description: '';
    displayName: 'White Paper';
    pluralName: 'white-papers';
    singularName: 'white-paper';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    form: Schema.Attribute.Component<'form.case-study-form', false>;
    global_resource_type: Schema.Attribute.Relation<
      'oneToOne',
      'api::global-resource-type.global-resource-type'
    >;
    hero_section: Schema.Attribute.Component<'case-study.hero-section', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::white-paper.white-paper'
    > &
      Schema.Attribute.Private;
    preview: Schema.Attribute.Component<'ebooks.preview', false>;
    publishedAt: Schema.Attribute.DateTime;
    rich_text: Schema.Attribute.RichText;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    slug: Schema.Attribute.String & Schema.Attribute.Required;
    title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiWhitePapersListingPageWhitePapersListingPage
  extends Struct.SingleTypeSchema {
  collectionName: 'white_papers_listing_pages';
  info: {
    displayName: 'white-papers listing page';
    pluralName: 'white-papers-listing-pages';
    singularName: 'white-papers-listing-page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    hero_section: Schema.Attribute.Component<'case-study.hero-section', false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::white-papers-listing-page.white-papers-listing-page'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    seo: Schema.Attribute.Component<'seo.seo', false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginContentReleasesRelease
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_releases';
  info: {
    displayName: 'Release';
    pluralName: 'releases';
    singularName: 'release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    actions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    releasedAt: Schema.Attribute.DateTime;
    scheduledAt: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Schema.Attribute.Required;
    timezone: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_release_actions';
  info: {
    displayName: 'Release Action';
    pluralName: 'release-actions';
    singularName: 'release-action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentType: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    entryDocumentId: Schema.Attribute.String;
    isEntryValid: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    release: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::content-releases.release'
    >;
    type: Schema.Attribute.Enumeration<['publish', 'unpublish']> &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
  collectionName: 'i18n_locale';
  info: {
    collectionName: 'locales';
    description: '';
    displayName: 'Locale';
    pluralName: 'locales';
    singularName: 'locale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::i18n.locale'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.SetMinMax<
        {
          max: 50;
          min: 1;
        },
        number
      >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflow
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows';
  info: {
    description: '';
    displayName: 'Workflow';
    name: 'Workflow';
    pluralName: 'workflows';
    singularName: 'workflow';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentTypes: Schema.Attribute.JSON &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'[]'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    stageRequiredToPublish: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::review-workflows.workflow-stage'
    >;
    stages: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflowStage
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows_stages';
  info: {
    description: '';
    displayName: 'Stages';
    name: 'Workflow Stage';
    pluralName: 'workflow-stages';
    singularName: 'workflow-stage';
  };
  options: {
    draftAndPublish: false;
    version: '1.1.0';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    color: Schema.Attribute.String & Schema.Attribute.DefaultTo<'#4945FF'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    permissions: Schema.Attribute.Relation<'manyToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    workflow: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::review-workflows.workflow'
    >;
  };
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
  collectionName: 'files';
  info: {
    description: '';
    displayName: 'File';
    pluralName: 'files';
    singularName: 'file';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    alternativeText: Schema.Attribute.String;
    caption: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ext: Schema.Attribute.String;
    folder: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'> &
      Schema.Attribute.Private;
    folderPath: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    formats: Schema.Attribute.JSON;
    hash: Schema.Attribute.String & Schema.Attribute.Required;
    height: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.file'
    > &
      Schema.Attribute.Private;
    mime: Schema.Attribute.String & Schema.Attribute.Required;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    previewUrl: Schema.Attribute.String;
    provider: Schema.Attribute.String & Schema.Attribute.Required;
    provider_metadata: Schema.Attribute.JSON;
    publishedAt: Schema.Attribute.DateTime;
    related: Schema.Attribute.Relation<'morphToMany'>;
    size: Schema.Attribute.Decimal & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String & Schema.Attribute.Required;
    width: Schema.Attribute.Integer;
  };
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
  collectionName: 'upload_folders';
  info: {
    displayName: 'Folder';
    pluralName: 'folders';
    singularName: 'folder';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    children: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.folder'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    files: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.file'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.folder'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    parent: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'>;
    path: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    pathId: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.String & Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    >;
  };
}

export interface PluginUsersPermissionsUser
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'user';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
    confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    > &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ContentTypeSchemas {
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::permission': AdminPermission;
      'admin::role': AdminRole;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'admin::user': AdminUser;
      'api::about-us.about-us': ApiAboutUsAboutUs;
      'api::ai-readiness-component.ai-readiness-component': ApiAiReadinessComponentAiReadinessComponent;
      'api::ai-readiness.ai-readiness': ApiAiReadinessAiReadiness;
      'api::all-resources-page.all-resources-page': ApiAllResourcesPageAllResourcesPage;
      'api::all-service-page.all-service-page': ApiAllServicePageAllServicePage;
      'api::author.author': ApiAuthorAuthor;
      'api::award.award': ApiAwardAward;
      'api::blog-listing-page.blog-listing-page': ApiBlogListingPageBlogListingPage;
      'api::blog.blog': ApiBlogBlog;
      'api::career.career': ApiCareerCareer;
      'api::case-study-listing-page.case-study-listing-page': ApiCaseStudyListingPageCaseStudyListingPage;
      'api::case-study.case-study': ApiCaseStudyCaseStudy;
      'api::city-service-page.city-service-page': ApiCityServicePageCityServicePage;
      'api::cloud-consulting.cloud-consulting': ApiCloudConsultingCloudConsulting;
      'api::cloud-migration-component.cloud-migration-component': ApiCloudMigrationComponentCloudMigrationComponent;
      'api::cloud-migration-cost-calculator.cloud-migration-cost-calculator': ApiCloudMigrationCostCalculatorCloudMigrationCostCalculator;
      'api::contact-us.contact-us': ApiContactUsContactUs;
      'api::cookie-policy.cookie-policy': ApiCookiePolicyCookiePolicy;
      'api::e-book.e-book': ApiEBookEBook;
      'api::e-books-listing-page.e-books-listing-page': ApiEBooksListingPageEBooksListingPage;
      'api::event-listing-page.event-listing-page': ApiEventListingPageEventListingPage;
      'api::event-main-page.event-main-page': ApiEventMainPageEventMainPage;
      'api::footer.footer': ApiFooterFooter;
      'api::form.form': ApiFormForm;
      'api::global-industry.global-industry': ApiGlobalIndustryGlobalIndustry;
      'api::global-resource-type.global-resource-type': ApiGlobalResourceTypeGlobalResourceType;
      'api::global-service.global-service': ApiGlobalServiceGlobalService;
      'api::global-video-tag.global-video-tag': ApiGlobalVideoTagGlobalVideoTag;
      'api::header.header': ApiHeaderHeader;
      'api::home-page.home-page': ApiHomePageHomePage;
      'api::industry.industry': ApiIndustryIndustry;
      'api::l2-service-page.l2-service-page': ApiL2ServicePageL2ServicePage;
      'api::l3-service-page.l3-service-page': ApiL3ServicePageL3ServicePage;
      'api::new.new': ApiNewNew;
      'api::partner.partner': ApiPartnerPartner;
      'api::podcast-page.podcast-page': ApiPodcastPagePodcastPage;
      'api::pr.pr': ApiPrPr;
      'api::privacy-policy.privacy-policy': ApiPrivacyPolicyPrivacyPolicy;
      'api::retail-component.retail-component': ApiRetailComponentRetailComponent;
      'api::retail-page.retail-page': ApiRetailPageRetailPage;
      'api::search.search': ApiSearchSearch;
      'api::solution.solution': ApiSolutionSolution;
      'api::test.test': ApiTestTest;
      'api::testimonial.testimonial': ApiTestimonialTestimonial;
      'api::thank-you.thank-you': ApiThankYouThankYou;
      'api::trusted-partner.trusted-partner': ApiTrustedPartnerTrustedPartner;
      'api::video-page.video-page': ApiVideoPageVideoPage;
      'api::video.video': ApiVideoVideo;
      'api::white-paper.white-paper': ApiWhitePaperWhitePaper;
      'api::white-papers-listing-page.white-papers-listing-page': ApiWhitePapersListingPageWhitePapersListingPage;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::review-workflows.workflow': PluginReviewWorkflowsWorkflow;
      'plugin::review-workflows.workflow-stage': PluginReviewWorkflowsWorkflowStage;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
    }
  }
}
