{"name": "mtl-strapi-cms", "version": "0.1.0", "private": true, "description": "A Strapi application", "license": "MIT", "author": {"name": "A Strapi developer"}, "scripts": {"build": "strapi build", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi"}, "dependencies": {"@_sh/strapi-plugin-ckeditor": "^6.0.2", "@strapi-community/strapi-provider-upload-google-cloud-storage": "^5.0.5", "@strapi/plugin-cloud": "5.21.0", "@strapi/plugin-users-permissions": "5.21.0", "@strapi/provider-upload-aws-s3": "^5.19.0", "@strapi/strapi": "5.21.0", "better-sqlite3": "^8.6.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/minimatch": "^6.0.0"}, "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}