/*
 *
 * HomePage
 *
 */

import React from "react";
// import PropTypes from 'prop-types';
import pluginId from "../../pluginId";

const HomePage = () => {
  return (
    <div style={{ padding: "20px" }}>
      <h1>
        <b style={{ color: "white" }}>
          This is the detail page of plugin: {pluginId}
        </b>
      </h1>
      <br />
      <p style={{ color: "white", paddingBottom: "5px" }}>
        The Publish-to-Staging plugin integrates a staging build trigger button
        into the content manager.
      </p>
      <p style={{ color: "white" }}>
        Users can trigger staging builds by clicking this button to push all the
        latest changes to staging.
      </p>
    </div>
  );
};

export default HomePage;
